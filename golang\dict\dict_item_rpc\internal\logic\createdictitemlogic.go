package logic

import (
	"context"
	"dict_rpc/dict"
	"errors"

	"dict_item_rpc/dict_item"
	"dict_item_rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type CreateDictItemLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateDictItemLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateDictItemLogic {
	return &CreateDictItemLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 创建字典项
func (l *CreateDictItemLogic) CreateDictItem(in *dict_item.CreateDictItemReq) (*dict_item.CreateDictItemResp, error) {
	// todo: add your logic here and delete this line
	
	_, err := l.svcCtx.DictRpc.GetDict(l.ctx, &dict.GetDictReq{
		Id: in.DictId,
	})
	if err!=nil{
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &dict_item.CreateDictItemResp{
				Id:      0,
				Message: "字典不存在",
			}, nil
		}
		return &dict_item.CreateDictItemResp{
			Id:      0,
			Message: "查询字典失败",
		}, nil
	}

	

	return &dict_item.CreateDictItemResp{}, nil
}
