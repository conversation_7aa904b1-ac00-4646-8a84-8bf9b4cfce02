package svc

import (
	"dict_item_rpc/internal/config"
	"dict_category_rpc/model"

	"dict_rpc/dictservice"

	"github.com/zeromicro/go-zero/zrpc"
)

type ServiceContext struct {
	Config  config.Config
	DictRpc dictservice.DictService
	DictCategoryModel model.DictItemModel
}

func NewServiceContext(c config.Config) *ServiceContext {
	return &ServiceContext{
		Config:  c,
		DictRpc: dictservice.NewDictService(zrpc.MustNewClient(c.DictRpc)),
		DictCategoryModel *model.NewDictC(model.NewDb(c.Mysql.DataSource)),
	}
}
